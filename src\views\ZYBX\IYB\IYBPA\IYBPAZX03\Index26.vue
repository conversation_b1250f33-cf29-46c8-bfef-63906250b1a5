<template>
    <div class="container_2208291100">
        <audio id="id_audio_2310081000" preload="auto" type="audio/wav">
        </audio>
        <div class="banner">
            <img src="@/assets/imgs/IYB/IYBPA/IYBPAZX03/img01_18.png">
            <div class="logo-x">
                <img class="logo-gr logo" src="@/assets/imgs/logo/guoren_4.png">
                <img class="logo-tk logo" v-if="!isTKHidden1" src="@/assets/imgs/logo/taikang_3.png">
                <img class="logo-pa logo" v-if="!isPAHidden1" src="@/assets/imgs/logo/pingan_5.png">
            </div>
            <div class="banner-tag" @click="onViewDetail('活动规则')">活动规则</div>
        </div>
        <HHInputBox :obj="orderInfo" :highlight="highlight" class="input-x" @focus="onTextFocus" @input="onTextInput"
            @blur="onTextBlur" @submit="submit">
            <template #policy>
                <div v-if="isStep2" class="policy-x">
                    <van-icon class="policy-icon" :name="orderInfo.checked ? 'checked' : 'circle'"
                        @click="orderInfo.checked = !orderInfo.checked" />
                    我已确认
                    <span class="policy-txt" @click.stop="onReadPolicy('健康告知')">《健康告知》</span>
                    <span class="policy-txt" @click.stop="onReadPolicy('投保须知')">《投保须知》</span>
                    <span class="policy-txt" @click.stop="onReadPolicy('保险条款')">《保险条款》</span>
                    <span v-if="!isPAHidden1" class="policy-txt" @click.stop="onReadPolicy('投保人声明')">《投保人声明》</span>
                    <span v-if="!isTPHidden1" class="policy-txt" @click.stop="onReadPolicy('产品说明')">《产品说明》</span>
                    <span class="policy-txt" @click.stop="onReadPolicy('责任免除')">《责任免除》</span>
                    <span class="policy-txt" @click.stop="onReadPolicy('隐私条款')">《隐私条款》</span>
                    <span class="policy-txt" @click.stop="onReadPolicy('活动规则')">《活动规则》</span>并接受承保保司提供保险咨询服务。
                </div>
            </template>
        </HHInputBox>
        <van-swipe class="introduce" indicator-color="#1a5deb">
            <van-swipe-item v-if="!isPAHidden1"><img alt=""
                    src="@/assets/imgs/IYB/IYBGRZX01/img03_1.png"></van-swipe-item>
            <van-swipe-item><img alt="" src="@/assets/imgs/IYB/IYBGRZX01/img02_1.png"></van-swipe-item>
            <van-swipe-item v-if="!isTPHidden1"><img alt=""
                    src="@/assets/imgs/IYB/IYBGRZX01/img04_1.png"></van-swipe-item>
            <van-swipe-item v-if="!isTKHidden1"><img alt=""
                    src="@/assets/imgs/IYB/IYBGRZX01/img09_1.png"></van-swipe-item>
        </van-swipe>
        <img v-if="!isTPHidden1" alt="" class="introduce" src="@/assets/imgs/IYB/IYBPA/IYBPAZX03/img04_3.png">
        <div class="copyright">
            <p v-if="insCompany.extra && isProviderShow">{{ insCompany.extra }}</p>
            <p v-if="!isPAHidden1">平安产险重大疾病保险条款注册号：<br />C00001732612024062802583</p>
            <p v-if="!isTPHidden1">太平任逍遥意外伤害保险（互联网专属）条款：<br />太平人寿[2024]意外伤害保险006号</p>
            <p>国任无忧重疾赠险条款注册号：<br />C00014232612023080323521</p>
            <p v-if="!isTKHidden1">泰康e顺短期交通意外伤害保险(互联网)：<br />泰康人寿（2021）意外伤害保险109号</p>
            <p v-if="!insCompany.isHidden">版权所有©{{ insCompany.name }}</p>
            <div class="beian">
                <p>互联网专属产品</p>
                <p>{{ insCompany.number }}</p>
            </div>
        </div>
        <div v-if="bottomButtonVisible" class="bottom-x" :class="{ 'bottom-x-animation': highlight.button }"
            @click="submit">
            免费领取
            <img v-if="highlight.button" alt="hand" class="hand" src="@/assets/imgs/common/icon_hand1.png">
        </div>
        <HHTabViewer :obj="policyObj" @ok="checkPolicy" @view="onViewDetail"></HHTabViewer>
        <HHPolicyViewer :obj="policyObj"></HHPolicyViewer>
        <HHNextPopup :obj="nextObj" @click="jumpNextLink"></HHNextPopup>
        <HHPrevPopup :obj="prevObj" @click="jumpPrevLink"></HHPrevPopup>
        <HHWarnHint :obj="orderInfo"></HHWarnHint>
        <IYBRecord1 :obj="orderInfo"></IYBRecord1>
        <!-- H5通话功能管理器（不显示弹窗） -->
        <H5CallManager ref="h5CallManager" :channel="orderInfo.channel" @h5-call-ready="onH5CallReady"
            @h5-call-error="onH5CallError"></H5CallManager>
    </div>
</template>

<script>
import HHWarnHint from "./components/HHWarnHint";
import HHInputBox from "./components/HHInputBox3";
import HHNextPopup from "./components/HHNextPopup";
import HHPrevPopup from "./components/HHPrevPopup";
import HHPolicyViewer from "./components/HHPolicyViewer";
import HHTabViewer from "./components/HHTabViewer";
import IYBRecord1 from "@/views/components/IYBRecord1";
import H5CallManager from "@/components/H5CallManager";
import { fetchIYBWXOpenId } from "@/api/wx-api";
import { domainPathMap, is_server_phone, findDomainAbrr, audioObj1 } from "@/views/ZYBX/src";
import { createOrderInfo, eventTracking, loadOrderInfo, saveOrderInfo, showToast, domainTracking } from "./src";
import { reviseIdCard, reviseName, GetAge, isAIChannel, isCardNo, isMaskedAndT1Phone, isPersonName, isPhoneNum, TraceLogInfoKeys, url_safe_b64_encode } from "@/assets/js/common";
import { createMultiFreeOrder, fetchRoundRobinWithAgeZXResultNew, fetchStarPhoneV4, userWeComInfo, fetchNsfInfo, bindPhoneAndPrivacy } from '@/api/insurance-api';
import { openNewTab } from '@/utils/window';
export default {
    name: "IYBPAZX03Index26",
    data() {
        const orderInfo = createOrderInfo();
        return {
            isTPHidden: false,
            isTPHidden1: false,
            isPAHidden1: false,
            isTKHidden1: false,
            isProviderShow: false,
            orderInfo: orderInfo,
            policyObj: { visible: false, page: '', visible1: false, page1: '', isTPHidden1: false, isPAHidden1: false, isTKHidden1: false },
            prevObj: { visible: false, showTimer: false, path: '' },
            nextObj: { visible: false, showTimer: false, path: '' },
            bottomButtonVisible: false,
            prevName: '',
            audioObj: audioObj1,
            // 语音交互相关
            userActivityTimer: null,
            hasTriggeredVoiceGuide: false,
            isVoiceAuthRejected: false,
            lastActivityTime: Date.now(),
            voiceInteractionNodes: [], // 记录语音交互触发节点
            // 高光提示状态
            highlight: {
                name: false,
                idCard: false,
                phone: false,
                button: false,
            }
        }
    },
    components: {
        IYBRecord1,
        HHPrevPopup,
        HHNextPopup,
        HHInputBox,
        HHPolicyViewer,
        HHTabViewer,
        HHWarnHint,
        H5CallManager,
    },
    computed: {
        isStep2() {
            return this.orderInfo.step == 'step2';
        },
    },
    mounted() {
        // 重置高光提示
        this.resetHighlight();

        if (history.scrollRestoration) {
            history.scrollRestoration = 'manual';
        }

        this.init();

        this.$nextTick(() => {
            this.actionButtonObserver();
            this.pushHistory();
            window.addEventListener("popstate", this.stackPopHandle);
            this.initUserActivityDetection();
        });

        this.makeAudioWork();

        // 更新高光提示
        this.autoUpdateHighlight({
            playHintAudioIfAtNameOrIdCard: true,
        });
    },
    beforeDestroy() {
        window.removeEventListener("popstate", this.stackPopHandle);
        this.clearUserActivityTimer();
    },
    methods: {
        makeAudioWork() {
            const func = () => {
                try {
                    const oAudio = document.querySelector('#id_audio_2310081000');
                    if (oAudio && !oAudio.alreadyPlay) {
                        oAudio.play().then(() => {
                            oAudio.alreadyPlay = true;
                        });

                        oAudio.addEventListener("canplay", () => {
                            console.log('音频可播放', Date.now());
                            oAudio.play(); // 音频可流畅播放时触发
                        });

                        // 监听播放结束事件
                        oAudio.addEventListener('ended', () => {
                            console.log('音频播放结束', Date.now());
                            for (let key in this.audioObj) {
                                const arr = this.audioObj[key];
                                const idx = arr.indexOf(oAudio.src);
                                if (idx >= 0) {
                                    setTimeout(() => {
                                        const tmp = arr[idx + 1];
                                        if (tmp) {
                                            oAudio.src = tmp;
                                            oAudio.load();
                                        }
                                    }, 3000);

                                    return;
                                }
                            }
                        });
                    }
                } catch (e) {

                }
            };

            window.onclick = func;
            window.ontouchstart = func;
        },
        refreshAudioSrc(key) {
            const oAudio = document.querySelector('#id_audio_2310081000');
            if (!oAudio) {
                return;
            }
            if (oAudio.src.indexOf(key) >= 0) {
                return;
            }
            const arr = this.audioObj[key];
            if (!arr) {
                return '';
            }
            oAudio.src = arr[0];
            oAudio.load();
        },
        //初始化
        init() {
            const inQry = this.$route.query || {};
            const orderInfo = loadOrderInfo();

            this.isTPHidden = inQry.isShow != 1;
            this.isTPHidden1 = inQry.isShow1 != 1;
            this.isPAHidden1 = inQry.isShow2 != 1;
            this.isTKHidden1 = inQry.isShow3 != 1;
            this.isProviderShow = inQry.isShow0 == 1;
            this.policyObj.isTPHidden1 = this.isTPHidden1;
            this.policyObj.isPAHidden1 = this.isPAHidden1;
            this.policyObj.isTKHidden1 = this.isTKHidden1;

            Object.assign(this.orderInfo, orderInfo);
            this.orderInfo.page = 'IYBPAZX03Index26';
            this.orderInfo.sourcePage = '保通平安交通重疾聚合V250703';
            this.orderInfo.channel = inQry.channel || orderInfo.channel || '1';
            this.orderInfo.channelCode = inQry.cld || orderInfo.channelCode || '';
            this.orderInfo.encryptNumber = inQry.encryptNumber || orderInfo.encryptNumber || '';
            this.orderInfo.mc = inQry.mc || this.orderInfo.mc || '';
            this.orderInfo.checked = this.orderInfo.channel >= 1000;

            if (this.orderInfo.received) {
                this._entryReport();
                return this.fetchNsfCode();
            }

            inQry.externalUserId = inQry.externalUserId || ''; // 保通企微
            inQry.userId = inQry.userId || ''; // 保通企微

            Object.assign(this.orderInfo, inQry);
            const { phoneNo, starPhone, mTel } = this.orderInfo;
            if (isPhoneNum(phoneNo) || isMaskedAndT1Phone(starPhone, mTel)) {
                this.orderInfo.step = 'step2';
            } else {
                this.orderInfo.step = 'step1';
            }

            this.fetchPhoneNumber();

            this.fetchOpenId();
        },

        fetchOpenId() {
            const { sid, openId, biz_no } = this.orderInfo;
            if (!sid || !biz_no || openId) return;

            fetchIYBWXOpenId(sid, biz_no).then(res => {
                const { code, result } = res.data;
                if (code == 0) {
                    if (result && result.openid) {
                        this.orderInfo.openId = result.openid;
                        saveOrderInfo(this.orderInfo);
                    }
                }
            });
        },

        actionButtonObserver() {
            if (window.IntersectionObserver) {
                const observer = new IntersectionObserver((entries,) => {
                    this.bottomButtonVisible = !entries[0].isIntersecting;
                }, { threshold: 0.01 });

                const buttonNode = document.getElementById('id_action_button');
                buttonNode && observer.observe(buttonNode);
            }
        },

        fetchPhoneNumber() {
            const { m, phoneNo, starPhone, mTel, channelEnMContent, channelEnMCode } = this.orderInfo;
            if ((!m && !channelEnMContent) || isPhoneNum(phoneNo) || isMaskedAndT1Phone(starPhone, mTel)) {
                this.orderInfo.step == 'step2' ? this.refreshAudioSrc('shenfenzheng') : this.refreshAudioSrc('shoujihao');
                return this._entryReport();
            }

            const params = {};
            if (m) {
                params.encryptContent = m;
            } else {
                params.channelEnMContent = channelEnMContent;
                params.channelEnMCode = channelEnMCode;
            }
            fetchStarPhoneV4(params).then(res => {
                const { encryptPhone, showPhone } = res.data;
                this.orderInfo.mTel = encryptPhone;
                this.orderInfo.starPhone = showPhone;
                this.orderInfo.phoneNo = showPhone;
                if (isMaskedAndT1Phone(showPhone, encryptPhone)) {
                    this.orderInfo.step = 'step2';
                }
                saveOrderInfo(this.orderInfo);
            }).finally(() => {
                this.orderInfo.step == 'step2' ? this.refreshAudioSrc('shenfenzheng') : this.refreshAudioSrc('shoujihao');
                return this._entryReport();
            });
        },

        bindPhoneAction() {
            const { channel, phoneNo, mTel, encryptNumber } = this.orderInfo;
            if (!encryptNumber) {
                return;
            }

            const params = {
                channelId: channel,
                relatePhone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                volcEncryptPhone: encryptNumber,
            }

            bindPhoneAndPrivacy(params).finally(() => {

            });
        },

        onReadPolicy(page) {
            this.policyObj.page = page;
            this.policyObj.visible = true;
        },

        onViewDetail(page) {
            this.policyObj.page1 = page;
            this.policyObj.visible1 = true;
        },

        checkPolicy() {
            this.orderInfo.checked = true;
            this.submit();
        },

        onTextInput({ key, value }) {
            // console.log('⌨️ [用户操作] 输入内容:', k、ey, '值:', value);
            let isChanged = false;
            const channel = this.orderInfo.channel;

            this.autoUpdateHighlight();

            if (key === 'phone' && isPhoneNum(value)) {
                if (is_server_phone(value)) {
                    this.orderInfo.phoneNo = '';
                    return showToast('请输入您本人的手机号码');
                }

                isChanged = true;
                this.recordVoiceInteractionNode('输入手机号');
                if (isAIChannel(channel)) {
                    this._actionTracking('首页-完成输入手机号');
                }
            } else if (key === 'idCard' && isCardNo(value)) {
                isChanged = true;
                this.recordVoiceInteractionNode('输入身份证号');
                if (isAIChannel(channel)) {
                    this._actionTracking('首页-完成输入身份证');
                }
            } else if (key === 'name' && isPersonName(value)) {
                isChanged = true;
                this.recordVoiceInteractionNode('输入姓名');
                if (this.prevName != value) {
                    this.prevName = value;
                    if (isAIChannel(channel)) {
                        this._actionTracking(`首页-完成输入姓名`);
                    }
                }
            } else {
                // 记录输入过程中的节点（即使输入不完整）
                if (key === 'phone' && value) {
                    this.recordVoiceInteractionNode('输入手机号');
                } else if (key === 'idCard' && value) {
                    this.recordVoiceInteractionNode('输入身份证号');
                } else if (key === 'name' && value) {
                    this.recordVoiceInteractionNode('输入姓名');
                }
            }

            if (!isChanged) return;

            saveOrderInfo(this.orderInfo);
        },

        onTextFocus({ key, value }) {
            // console.log('🎯 [用户操作] 聚焦输入框:', key, '当前值:', value);
            const channel = this.orderInfo.channel;

            // 记录语音交互节点
            if (key === 'idCard') {
                this.recordVoiceInteractionNode('聚焦身份证号');
                if (!isAIChannel(channel)) return;
                if (!value) {
                    this._actionTracking('首页-开始输入身份证');
                }
            } else if (key === 'name') {
                this.recordVoiceInteractionNode('聚焦姓名');
                if (!isAIChannel(channel)) return;
                if (!value) {
                    this._actionTracking('首页-开始输入姓名');
                }
            } else if (key === 'phone') {
                this.recordVoiceInteractionNode('聚焦手机号');
            }
        },

        onTextBlur({ key, value }) {
            // console.log('👋 [用户操作] 失焦输入框:', key, '当前值:', value);

            // 记录失焦节点
            if (key === 'idCard') {
                this.recordVoiceInteractionNode('失焦身份证号');
            } else if (key === 'name') {
                this.recordVoiceInteractionNode('失焦姓名');
            } else if (key === 'phone') {
                this.recordVoiceInteractionNode('失焦手机号');
            }
        },

        // 重置高光提示
        resetHighlight() {
            this.highlight = {
                phone: false,
                name: false,
                idCard: false,
                button: false
            };
        },

        // 自动更新高光提示
        autoUpdateHighlight(config = {}) {
            // 按顺序判断某项是否正确，不正确就显示高光，正确就继续判断后面一项。
            // 第一步：手机号——免费领取
            // 第二步：手机号——姓名——身份证号——免费领取

            // 准备表单信息
            const { name1, phoneNo, idCard1, starPhone } = this.orderInfo;

            // 重置高光提示
            this.resetHighlight();

            if (!this.isStep2) {
                // 第一步
                if (!isPhoneNum(phoneNo) && (phoneNo.length !== 11 || phoneNo !== starPhone)) {
                    // 手机号
                    return this.highlight.phone = true;
                }
                // 免费领取
                return this.highlight.button = true;
            } else {
                // 第二步
                if (!isPhoneNum(phoneNo) && (phoneNo.length !== 11 || phoneNo !== starPhone)) {
                    // 手机号
                    if (config.playHintAudioIfAtNameOrIdCard) {
                        this.refreshAudioSrc('zhiyin');
                    }
                    return this.highlight.phone = true;
                }
                if (!isPersonName(name1)) {
                    // 姓名
                    if (config.playHintAudioIfAtNameOrIdCard) {
                        this.refreshAudioSrc('zhiyin');
                    }
                    return this.highlight.name = true;
                }
                if (!isCardNo(idCard1)) {
                    // 身份证号
                    if (config.playHintAudioIfAtNameOrIdCard) {
                        this.refreshAudioSrc('zhiyin');
                    }
                    return this.highlight.idCard = true;
                }
                // 免费领取
                return this.highlight.button = true;
            }
        },

        submit() {
            this.autoUpdateHighlight();

            let name = reviseName(this.orderInfo.name1);
            let idCard = reviseIdCard(this.orderInfo.idCard1);
            if (!isPersonName(name)) {
                const temp = reviseName(this.orderInfo.idCard1);
                if (isPersonName(temp)) {
                    name = temp;
                }
            }

            if (!isCardNo(idCard)) {
                const temp = reviseIdCard(this.orderInfo.name1);
                if (isCardNo(temp)) {
                    idCard = temp;
                }
            }

            this.orderInfo.name1 = name;
            this.orderInfo.idCard1 = idCard;

            const { name1, phoneNo, idCard1, starPhone, checked } = this.orderInfo;

            let message = '';
            if (this.orderInfo.step != 'step2') {
                if (!isPhoneNum(phoneNo) && (phoneNo.length != 11 || phoneNo != starPhone)) {
                    message = '请输入正确的手机号';
                }
            }

            if (message) {
                this.orderInfo.step = 'step1';
                return showToast(message);
            }

            if (this.orderInfo.step != 'step2') {
                this.orderInfo.step = 'step2';
                this.userWeComInfo();
                saveOrderInfo(this.orderInfo);
                this.bindPhoneAction();
                domainTracking(this.orderInfo);
                this.refreshAudioSrc('shenfenzheng');
                this.autoUpdateHighlight();
                return this._actionTracking('首页-点击第一步立即领取');
            }

            if (!isPersonName(name1)) {
                message = '请输入正确的姓名';
            } else if (!isCardNo(idCard1)) {
                message = '请输入正确的身份证号码';
            }

            if (message) {
                return showToast(message);
            }

            if (!checked) {
                return this.onReadPolicy('投保须知');
            }

            this._actionTracking('首页-点击第二步立即领取');

            this.submitOrder();

            this.fetchNsfCode();
        },

        fetchNsfCode() {
            const { channel, phoneNo, mTel } = this.orderInfo;
            const phone = isPhoneNum(phoneNo) ? phoneNo : mTel;

            this.$toast.loading({
                message: '数据处理中\n请稍候',
                position: 'middle',
                forbidClick: true,
                duration: 0,
            });

            const params = {
                phone,
                channelId: channel,
                infoKey: TraceLogInfoKeys.iyb_pa_money_disease_send,
                nsfKey: 'zx_to_mf_nsf_key',
            }

            fetchNsfInfo(params).then(res => {
                const { code, data } = res.data || {};
                if (code == 2000) {
                    this.orderInfo.nsfCode = data;
                }
            }).finally(() => {
                this.pushToResult(true);
            });
        },

        submitOrder() {
            const { name1, idCard1, phoneNo, relation, recordSetId, visitorId, traceBackUuid, openId, biz_no } = this.orderInfo;
            const { infoNo, channel, page, mTel, channelCode, sourcePage, mc } = this.orderInfo;

            const planKeys = [TraceLogInfoKeys.iyb_gr_disease_send, TraceLogInfoKeys.iyb_tp_rxy_send, TraceLogInfoKeys.iyb_zy_tk_life_ftb_send];
            if (!this.isPAHidden1) {
                planKeys.push(TraceLogInfoKeys.iyb_pa_money_disease_send);
            }
            // if (!this.isTPHidden1) {
            //     planKeys.push(TraceLogInfoKeys.iyb_tp_rxy_send);
            // }

            const params = {
                page,
                sourcePage,
                infoNo,
                holderName: name1,
                holderIdCard: idCard1,
                holderPhone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                relation,
                planKeys: planKeys,
                channelId: channel,
                operatorPhone: mTel || phoneNo,
                channelCode: channelCode || '',
                traceBackUuid: traceBackUuid,
                origin: mc,
            }

            const domain = findDomainAbrr();
            const extendParams = { mc2: mc, domain, recordSetId, visitorId };
            if (openId && biz_no) {
                extendParams.openId = openId;
                extendParams.wechatService = biz_no;
            }
            params.extendParams = JSON.stringify(extendParams);

            createMultiFreeOrder(params).finally(() => {
            });
        },

        userWeComInfo() {
            const { externalUserId, userId, phoneNo, mTel, channel } = this.orderInfo;
            if (!externalUserId) return;

            const params = {
                channelId: channel,
                externalUserId,
                serviceId: userId,
                phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
            }
            userWeComInfo(params).then(res => {

            });
        },

        pushToResult(save) {
            if (save) {
                this.orderInfo.received = 1;
                saveOrderInfo(this.orderInfo);
            }

            this.fetchNextPath();
            this.bindPhoneAction();
        },

        fetchNextPath() {
            const { channel, phoneNo, idCard1, mTel, nsfCode } = this.orderInfo;

            let robinKey = nsfCode == 1 ? 'iyb_pa_to_mf' : 'iyb_pa_to_mf';

            if (robinKey == 'iyb_pa_to_mf' && window.location.href.indexOf('zhelibao.com') >= 0) {
                robinKey = 'iyb_zlb_pa_to_mf'
            }

            const params = {
                channelId: channel,
                phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                idCard: idCard1,
                robinKey: robinKey,
            }

            fetchRoundRobinWithAgeZXResultNew(params).then(result => {
                const { path } = result.data || {};
                this.nextObj.path = path || '';
            }).finally(() => {
                this.$toast.clear(true);

                if (channel == 101) {
                    this.nextObj.path = 'IYBTK10Index1';
                }

                if (!domainPathMap[this.nextObj.path]) {
                    this.nextObj.path = 'IYBTK10Index1';
                }

                this.nextObj = { ...this.nextObj, visible: true, showTimer: true };
            });
        },

        jumpNextLink() {
            const { channelCode, channel, page, sourcePage, } = this.orderInfo;
            const { name1, idCard1, phoneNo, mTel, starPhone, openId, biz_no, mc } = this.orderInfo;

            this._actionTracking(`首页-点击弹框好的马上去(${this.nextObj.path})`);

            // 计算年龄
            const relation = GetAge(idCard1) < 18 ? 3 : 1;

            const params = {
                channel, cld: channelCode, relation, source: page, sourcePage, mTel, openId, biz_no, mc, action: 'follow',
                [`name${relation}`]: name1, [`idCard${relation}`]: idCard1,
            }

            if (isPhoneNum(phoneNo)) {
                params.phoneNo = phoneNo;
                params.starPhone = '';
            } else {
                params.phoneNo = '';
                params.starPhone = starPhone;
            }

            const bizParams = url_safe_b64_encode(JSON.stringify(params));
            const href = domainPathMap[this.nextObj.path];
            setTimeout(() => {
                // window.location.href = `${href}?bizParams=${bizParams}`;
                openNewTab(`${href}?bizParams=${bizParams}`);
            }, 250);
        },

        fetchPrevPath() {
            const { channel, phoneNo, idCard1, mTel } = this.orderInfo;

            const params = {
                channelId: channel,
                phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                robinKey: 'iyb_pa_send_back',
            }

            if (isCardNo(idCard1)) {
                params.idCard = idCard1;
            }

            fetchRoundRobinWithAgeZXResultNew(params).then(res => {
                const { path } = res.data || {};
                this.prevObj.path = path || '';
            }).finally(() => {
                this.backResultHandler();
            });
        },

        backResultHandler() {
            if (!domainPathMap[this.prevObj.path]) {
                this.prevObj.path = 'IYBTK10Index1';
            }

            this.prevObj = { ...this.prevObj, visible: true };
            this._actionTracking(`首页-点击返回按钮(${this.prevObj.path})`);
        },

        jumpPrevLink() {
            this._actionTracking(`首页-点击返回弹框图片(${this.prevObj.path})`);

            const { channel, name1, phoneNo, idCard1, mTel, page, channelCode, sourcePage, starPhone, openId, mc } = this.orderInfo;

            const relation = GetAge(idCard1) < 18 && GetAge(idCard1) > 0 ? 3 : 1;

            const params = {
                channel, cld: channelCode, relation, source: page, sourcePage, mTel, openId, mc, action: 'back',
                [`name${relation}`]: name1, [`idCard${relation}`]: idCard1,
            }

            if (isPhoneNum(phoneNo)) {
                params.phoneNo = phoneNo;
                params.starPhone = '';
            } else {
                params.phoneNo = '';
                params.starPhone = starPhone;
            }

            const bizParams = url_safe_b64_encode(JSON.stringify(params));
            const href = domainPathMap[this.prevObj.path];
            setTimeout(() => {
                // window.location.href = `${href}?bizParams=${bizParams}`;
                openNewTab(`${href}?bizParams=${bizParams}`);
            }, 250);
        },

        _entryReport() {
            const { timing } = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const { domContentLoadedEventEnd, fetchStart } = timing || {};
            this._actionTracking('首页', domContentLoadedEventEnd - fetchStart);

            domainTracking(this.orderInfo);

            if (!/^\d+$/.test(this.orderInfo.channel)) {
                this.orderInfo.channel = '333411';
            }
        },

        _actionTracking(name, time) {
            eventTracking(this.orderInfo, name, time);
        },

        pushHistory() {
            const state = { title: "title", url: "#" };
            window.history.pushState(state, "title", "");
        },

        stackPopHandle() {
            if (this.isStep2) {
                this.orderInfo.step = 'step1';
                this.refreshAudioSrc('shoujihao');
                saveOrderInfo(this.orderInfo);
            } else {
                this.refreshAudioSrc('wanliu');
                this.fetchPrevPath();
            }
            this.pushHistory();
        },

        // 用户活动检测相关方法
        initUserActivityDetection() {
            // console.log('🎯 [语音交互] 初始化用户活动检测');

            // 记录进入页面的埋点
            this.recordVoiceInteractionNode('进入页面');

            // 监听用户活动事件
            const events = ['click', 'touchstart', 'scroll', 'keydown', 'input'];
            // console.log('🎯 [语音交互] 开始监听用户活动事件:', events);
            events.forEach(event => {
                document.addEventListener(event, this.onUserActivity, { passive: true });
            });

            // 开始检测用户无操作
            this.startUserActivityTimer();
        },

        onUserActivity() {
            // console.log('👆 [语音交互] 检测到用户活动，重置计时器');
            this.lastActivityTime = Date.now();
            this.resetUserActivityTimer();
        },

        startUserActivityTimer() {
            // console.log('⏰ [语音交互] 开始10秒无操作检测计时器');
            this.clearUserActivityTimer();
            this.userActivityTimer = setTimeout(() => {
                // console.log('⏰ [语音交互] 10秒无操作时间到，触发语音引导');
                this.handleUserInactivity();
            }, 10000); // 10秒无操作
        },

        resetUserActivityTimer() {
            if (!this.hasTriggeredVoiceGuide && !this.isVoiceAuthRejected) {
                // console.log('🔄 [语音交互] 重置用户活动计时器');
                this.startUserActivityTimer();
            } else {
                // console.log('🚫 [语音交互] 不重置计时器 - 已触发引导:', this.hasTriggeredVoiceGuide, '已拒绝授权:', this.isVoiceAuthRejected);
            }
        },

        clearUserActivityTimer() {
            if (this.userActivityTimer) {
                // console.log('🗑️ [语音交互] 清除用户活动计时器');
                clearTimeout(this.userActivityTimer);
                this.userActivityTimer = null;
            }
        },

        handleUserInactivity() {
            // console.log('🚨 [语音交互] 处理用户无操作情况');

            // 如果已经触发过语音引导，则不再处理
            if (this.hasTriggeredVoiceGuide) {
                // console.log('🚫 [语音交互] 已经触发过语音引导，跳过处理');
                return;
            }

            // console.log('🎵 [语音交互] 首次触发语音引导流程');
            this.hasTriggeredVoiceGuide = true;

            // 播放引导语音（待白泽提供）
            this.playGuideAudio();

            // 直接尝试启用H5通话功能，这会触发浏览器的系统授权弹窗
            setTimeout(() => {
                // console.log('📞 [语音交互] 2秒后尝试启用H5通话');
                this.enableH5Call();
            }, 2000); // 语音播放2秒后启用H5通话
        },

        playGuideAudio() {
            // console.log('🎵 [语音交互] 播放引导语音');
            // TODO: 播放白泽提供的引导语音
            // 这里先用现有的音频播放逻辑
            this.refreshAudioSrc('bangka'); // 临时使用现有音频
            this.recordVoiceInteractionNode('播放引导语音');
        },

        enableH5Call() {
            // console.log('📞 [语音交互] 启用H5通话功能');

            // 设置埋点追踪数据
            this.setH5CallTraceData();

            // 直接调用H5CallManager的启动方法
            this.$nextTick(() => {
                // console.log('� [语音交互] 调用H5CallManager启动H5通话');
                // 通过ref调用H5CallManager的startH5Call方法
                if (this.$refs.h5CallManager) {
                    this.$refs.h5CallManager.startH5Call();
                } else {
                    // console.error('❌ [语音交互] 找不到H5CallManager组件引用');
                }
            });
        },

        setH5CallTraceData() {
            // console.log('📊 [语音交互] 设置H5通话埋点数据');

            // 设置H5通话埋点数据
            const traceData = {
                params: {
                    page: `H5通话-${this.orderInfo.page}`,
                    mobileId: this.orderInfo.phoneNo || this.orderInfo.mTel,
                    channel: this.orderInfo.channel,
                    infoKey: 'h5_voice_interaction',
                },
                pageName: this.orderInfo.page
            };

            // console.log('📊 [语音交互] 埋点数据:', traceData);

            // 存储到localStorage供H5CallManager使用
            if (typeof Storage !== 'undefined') {
                localStorage.setItem('H5CallTrace', JSON.stringify(traceData));
                // console.log('💾 [语音交互] 埋点数据已存储到localStorage');
            }
        },

        startUnidirectionalGuide() {
            // console.log('🎯 [语音交互] 启动单向全流程引导');
            // console.log('📋 [语音交互] 当前步骤:', this.orderInfo.step);

            // 实现单向全流程引导逻辑
            // 根据当前步骤播放相应的引导音频
            if (this.orderInfo.step === 'step1') {
                // console.log('📱 [语音交互] 引导用户输入手机号');
                // 第一步：引导输入手机号
                this.refreshAudioSrc('shoujihao');
                this.playUnidirectionalGuideAudio('请输入您的手机号码');
            } else if (this.orderInfo.step === 'step2') {
                // console.log('👤 [语音交互] 引导用户输入个人信息');
                // 第二步：引导输入姓名和身份证
                this.refreshAudioSrc('shenfenzheng');
                this.playUnidirectionalGuideAudio('请输入您的真实姓名和身份证号码');
            }

            // 设置定时检查，根据用户输入情况播放不同的引导音频
            this.startUnidirectionalGuideTimer();
        },

        playUnidirectionalGuideAudio(message) {
            // console.log('🔊 [语音交互] 播放单向引导语音:', message);
            // 这里可以使用TTS或预录音频播放引导语音
            // 暂时使用现有的音频播放逻辑
        },

        startUnidirectionalGuideTimer() {
            // 每30秒检查一次用户输入情况，播放相应的引导音频
            if (this.isVoiceAuthRejected) {
                setTimeout(() => {
                    this.checkAndPlayGuideAudio();
                }, 30000);
            }
        },

        checkAndPlayGuideAudio() {
            if (!this.isVoiceAuthRejected) return;

            const { step, phoneNo, name1, idCard1 } = this.orderInfo;

            if (step === 'step1') {
                if (!phoneNo || phoneNo.length < 11) {
                    this.playUnidirectionalGuideAudio('请完整输入您的11位手机号码');
                    this.startUnidirectionalGuideTimer();
                }
            } else if (step === 'step2') {
                if (!name1) {
                    this.playUnidirectionalGuideAudio('请输入您的真实姓名');
                    this.startUnidirectionalGuideTimer();
                } else if (!idCard1 || idCard1.length < 18) {
                    this.playUnidirectionalGuideAudio('请输入您的18位身份证号码');
                    this.startUnidirectionalGuideTimer();
                } else if (!this.orderInfo.checked) {
                    this.playUnidirectionalGuideAudio('请阅读并同意相关条款');
                    this.startUnidirectionalGuideTimer();
                }
            }
        },

        // H5通话事件处理
        onH5CallReady() {
            // console.log('✅ [语音交互] H5通话初始化成功！用户同意了系统授权');
            this._actionTracking('H5通话-初始化成功');

            // 记录当前触发双向语音交互的节点
            const currentNode = this.getCurrentInteractionNode();
            // console.log('📍 [语音交互] 当前触发节点:', currentNode);
            this.recordVoiceInteractionNode(`双向语音交互启动-${currentNode}`);

            // 播放欢迎语音
            this.playWelcomeAudio();
        },

        onH5CallError(error) {
            // console.log('❌ [语音交互] H5通话初始化失败！用户拒绝了系统授权');
            this._actionTracking('H5通话-初始化失败');
            // console.error('H5通话初始化失败:', error);

            // 记录用户拒绝授权
            this.isVoiceAuthRejected = true;
            this.recordVoiceInteractionNode('用户拒绝语音授权');
            this._actionTracking('语音授权-拒绝');

            // console.log('🔄 [语音交互] 回退到单向引导模式');
            // 如果H5通话失败（用户拒绝授权），回退到单向引导模式
            this.startUnidirectionalGuide();
        },

        getCurrentInteractionNode() {
            // 根据当前页面状态确定用户在哪个节点触发了双向语音交互
            const { step, phoneNo, name1, idCard1 } = this.orderInfo;

            if (step === 'step1') {
                if (!phoneNo) {
                    return '手机号输入前';
                } else if (phoneNo.length < 11) {
                    return '手机号输入中';
                } else {
                    return '手机号输入完成';
                }
            } else if (step === 'step2') {
                if (!name1 && !idCard1) {
                    return '个人信息输入前';
                } else if (!name1) {
                    return '姓名输入前';
                } else if (!idCard1) {
                    return '身份证输入前';
                } else {
                    return '个人信息输入完成';
                }
            }

            return '未知节点';
        },

        playWelcomeAudio() {
            // 播放双向语音交互的欢迎音频
            this.playUnidirectionalGuideAudio('您好，我是您的专属保险顾问，有什么可以帮助您的吗？');
        },

        // 记录语音交互触发节点
        recordVoiceInteractionNode(node) {
            const nodeData = {
                node: node,
                timestamp: Date.now(),
                step: this.orderInfo.step
            };

            this.voiceInteractionNodes.push(nodeData);
            // console.log('📝 [语音交互] 记录节点:', nodeData);

            // 如果启用了H5通话，记录埋点
            if (window.h5CallEnabled) {
                // console.log('📊 [语音交互] 发送埋点:', `语音交互节点-${node}`);
                this._actionTracking(`语音交互节点-${node}`);
            }
        },
    },
}

</script>

<style lang="less" scoped type="text/less">
/deep/ .van-swipe__indicator {
    background-color: #999999;
}

.container_2208291100 {
    width: 3.75rem;
    min-height: 100%;
    font-size: 0.15rem;
    background-color: #F8F8F8;

    img {
        display: block;
        max-width: 100%;
    }

    .banner {
        position: relative;

        .logo-x {
            position: absolute;
            left: 0.15rem;
            top: 0.10rem;

            display: flex;
            flex-direction: row;
            align-items: center;

            .logo-gr {
                width: 0.61rem;
            }

            .logo-tk {
                width: 0.64rem;
            }

            .logo-pa {
                width: 1.24rem;
            }

            .logo+.logo {
                margin-left: 0.10rem;
            }
        }

        .banner-tag {
            position: absolute;
            top: 0.12rem;
            right: 0;
            width: 0.7rem;
            padding: 0.04rem 0 0.03rem 0;
            font-size: 0.12rem;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 0.1rem 0 0 0.1rem;
            text-align: center;
            color: #fff;
        }
    }

    .input-x {
        margin: -0.65rem 0.15rem 0.1rem;
    }

    .introduce {
        margin: 0.15rem auto 0;
        width: 3.45rem;
        border-radius: 0.07rem;
    }

    .policy-x {
        padding: 0 0.15rem 0.15rem;
        font-size: 0.13rem;
        color: #333333;
        line-height: 0.2rem;
        text-align: justify;

        .policy-icon {
            color: #1a5deb;
            font-size: 0.15rem;
            vertical-align: -0.01rem;
        }

        .policy-txt {
            color: #1a5deb;
            font-weight: 500;
        }
    }

    .copyright {
        padding: 0.2rem 0 0.75rem;
        text-align: center;
        color: #888888;
        font-size: 0.12rem;
        font-weight: 500;
        line-height: 1.8;

        .beian {
            margin-top: 0.10rem;
            color: #888888;
        }
    }

    .bottom-x {
        position: fixed;
        margin: 0 auto;
        padding: 0.15rem 0;
        inset: auto 0 0.2rem 0;
        width: 3rem;
        text-align: center;
        font-size: 0.2rem;
        font-weight: 700;
        color: #ffffff;
        border-radius: 0.25rem;
        box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
        background: linear-gradient(270deg,
                rgb(255, 16, 46),
                rgb(253, 123, 69));
        animation: banner_btn 1.35s linear infinite;

        .hand {
            position: absolute;
            top: 0.15rem;
            left: 75%;
            width: 18%;
            animation: banner_hand 1.25s linear infinite;
        }

        @keyframes banner_btn {
            0% {
                transform: scale(1);
            }

            40% {
                transform: scale(1);
            }

            70% {
                transform: scale(0.95);
            }

            100% {
                transform: scale(1);
            }
        }

        @keyframes banner_hand {
            0% {
                transform: translate(-0.1rem, -0.1rem);
            }

            45% {
                transform: translate(0.1rem, 0.1rem);
            }

            70% {
                transform: translate(0.1rem, 0.1rem);
            }

            100% {
                transform: translate(-0.1rem, -0.1rem);
            }
        }
    }
}
</style>
